#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
直接测试分机通讯
"""

import socket
import time
from datetime import datetime

def debug_print(message):
    timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] {message}")

def encrypt_decrypt_data(data):
    """
    分机加密/解密方法（对称）
    1. 将字节前后倒转（第一个字节变最后一个）
    2. 对每个字节求补码
    """
    if not data:
        return data
        
    # 步骤1: 字节前后倒转
    reversed_data = data[::-1]
    
    # 步骤2: 对每个字节求补码
    encrypted_data = bytes(~b & 0xFF for b in reversed_data)
    
    return encrypted_data

def test_extension_communication():
    """测试分机通讯"""
    host = "*************"
    port = 3456
    
    try:
        # 创建TCP连接
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        
        debug_print(f"连接到分机 {host}:{port}")
        sock.connect((host, port))
        debug_print("连接成功")
        
        # 测试多个不同的命令
        test_commands = [
            # 测试分机状态命令
            "help",
            "status",
            # 测试rs485端口状态
            "rs485 -p dev/ttyS2 -b 9600 -t 1000",
            # 从站地址1，读取地址10000，4个寄存器
            "rs485 -p dev/ttyS2 -b 9600 -t 5000 01 03 27 10 00 04 4F 78",
            # 尝试不同的从站地址
            "rs485 -p dev/ttyS2 -b 9600 -t 5000 02 03 27 10 00 04 4E 88",
            "rs485 -p dev/ttyS2 -b 9600 -t 5000 03 03 27 10 00 04 4D B9",
        ]

        for i, test_command in enumerate(test_commands):
            debug_print(f"\n=== 测试命令 {i+1} ===")
            debug_print(f"发送测试命令: {test_command}")
            test_single_command(sock, test_command)

    except Exception as e:
        debug_print(f"连接错误: {e}")
    finally:
        try:
            sock.close()
            debug_print("连接已关闭")
        except:
            pass

def test_single_command(sock, test_command):
    """测试单个命令"""
    try:
        # 编码为GBK
        command_bytes = test_command.encode('gbk')
        debug_print(f"命令字节: {' '.join(f'{b:02X}' for b in command_bytes)} ({len(command_bytes)} 字节)")

        # 加密
        encrypted_command = encrypt_decrypt_data(command_bytes)
        debug_print(f"加密后: {' '.join(f'{b:02X}' for b in encrypted_command)} ({len(encrypted_command)} 字节)")

        # 发送
        sock.send(encrypted_command)
        debug_print("命令已发送")

        # 接收响应
        all_data = b''
        for i in range(5):  # 尝试接收5次
            try:
                sock.settimeout(3.0 if i == 0 else 0.5)
                data = sock.recv(1024)
                if data:
                    all_data += data
                    debug_print(f"接收第{i+1}次数据: {' '.join(f'{b:02X}' for b in data)} ({len(data)} 字节)")
                else:
                    debug_print(f"第{i+1}次接收无数据")
                    break
            except socket.timeout:
                debug_print(f"第{i+1}次接收超时")
                break

        if all_data:
            debug_print(f"总接收数据: {' '.join(f'{b:02X}' for b in all_data)} ({len(all_data)} 字节)")

            # 解密
            decrypted = encrypt_decrypt_data(all_data)
            debug_print(f"解密后字节: {' '.join(f'{b:02X}' for b in decrypted)} ({len(decrypted)} 字节)")

            try:
                response_str = decrypted.decode('gbk')
                debug_print(f"解密后字符串: '{response_str}'")
            except Exception as e:
                debug_print(f"解码失败: {e}")

        else:
            debug_print("没有接收到任何数据")

    except Exception as e:
        debug_print(f"测试命令错误: {e}")

    # 等待一下再发送下一个命令
    time.sleep(1)

if __name__ == "__main__":
    test_extension_communication()
