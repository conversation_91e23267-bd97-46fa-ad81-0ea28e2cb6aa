#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
分机通讯Modbus客户端
实现通过TCP/IP转RS485分机进行Modbus通讯的异步客户端
"""

import asyncio
import socket
from datetime import datetime
from collections.abc import Callable
from pymodbus.client.base import ModbusBaseClient
from pymodbus.framer import FramerType
from pymodbus.pdu import ModbusPDU
from pymodbus.transport import CommParams, CommType
from pymodbus.exceptions import ConnectionException
from pymodbus.logging import Log


class AsyncModbusExtensionClient(ModbusBaseClient):
    """
    通过分机进行Modbus通讯的异步客户端
    
    分机是一个TCP/IP转RS485的设备，内置自定义加密方法
    """
    
    def __init__(
        self,
        host: str,
        framer: FramerType = "rtu",
        retries: int = 3,
        reconnect_delay: float = 0.1,
        reconnect_delay_max: float = 300.0,
        trace_packet: Callable[[bool, bytes], bytes] | None = None,
        trace_pdu: Callable[[bool, ModbusPDU], ModbusPDU] | None = None,
        trace_connect: Callable[[bool], None] | None = None,
        verbose: bool = False,
    ):
        """
        初始化分机客户端

        :param host: 分机IP地址
        :param framer: 帧格式，默认rtu
        :param retries: 重试次数
        :param reconnect_delay: 重连延迟
        :param reconnect_delay_max: 最大重连延迟
        :param trace_packet: 数据包跟踪函数
        :param trace_pdu: PDU跟踪函数
        :param trace_connect: 连接跟踪函数
        :param verbose: 是否启用详细调试输出
        """
        # 创建通讯参数
        comm_params = CommParams(
            comm_type=CommType.TCP,
            host=host,
            port=3456,  # 固定端口
            timeout_connect=5.0,
            reconnect_delay=reconnect_delay,
            reconnect_delay_max=reconnect_delay_max,
        )
        
        # 初始化基类
        super().__init__(
            framer=framer,
            retries=retries,
            comm_params=comm_params,
            trace_packet=trace_packet,
            trace_pdu=trace_pdu,
            trace_connect=trace_connect,
        )
        
        # 分机特定参数（固定值）
        self.extension_host = host
        self.extension_port = 3456
        self.serial_port = "dev/ttyS2"
        self.baudrate = 9600
        self.timeout_ms = 1000

        # 调试选项
        self.verbose = verbose

        # 连接状态
        self._socket = None
        self._connected = False

    def _debug_print(self, message: str) -> None:
        """
        输出调试信息（仅在verbose模式下）

        :param message: 调试信息
        """
        if self.verbose:
            timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
            print(f"[{timestamp}] [分机调试] {message}")

    def _encrypt_decrypt_data(self, data: bytes) -> bytes:
        """
        分机加密/解密方法（对称）
        1. 将字节前后倒转（第一个字节变最后一个）
        2. 对每个字节求补码
        
        :param data: 待处理的数据
        :return: 处理后的数据
        """
        if not data:
            return data
            
        # 步骤1: 字节前后倒转
        reversed_data = data[::-1]
        
        # 步骤2: 对每个字节求补码
        encrypted_data = bytes(~b & 0xFF for b in reversed_data)
        
        return encrypted_data
    
    def _build_rs485_command(self, modbus_data: bytes) -> str:
        """
        构建rs485命令字符串
        
        :param modbus_data: Modbus RTU格式的字节数据
        :return: rs485命令字符串
        """
        # 将字节数据转换为十六进制字符串
        hex_data = ' '.join(f'{b:02X}' for b in modbus_data)
        
        # 构建rs485命令
        command = f"rs485 -p {self.serial_port} -b {self.baudrate} -t {self.timeout_ms} {hex_data}"
        
        return command
    
    def _parse_rs485_response(self, response: str) -> bytes:
        """
        解析rs485命令的响应
        
        :param response: 分机返回的十六进制字符串响应
        :return: 解析后的字节数据
        """
        # 移除可能的空白字符和换行符
        response = response.strip()
        
        # 分割十六进制字符串并转换为字节
        hex_parts = response.split()
        try:
            data = bytes(int(part, 16) for part in hex_parts if part)
            return data
        except ValueError as e:
            raise ConnectionException(f"无法解析分机响应: {response}, 错误: {e}")
    
    async def connect(self) -> bool:
        """
        连接到分机

        :return: 连接是否成功
        """
        try:
            if self.ctx.trace_connect:
                self.ctx.trace_connect(True)

            # 创建TCP连接到分机
            self._socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self._socket.settimeout(self.comm_params.timeout_connect)

            Log.debug("正在连接到分机 {}:{}", self.extension_host, self.extension_port)
            await asyncio.get_event_loop().run_in_executor(
                None, self._socket.connect, (self.extension_host, self.extension_port)
            )

            self._connected = True
            # 设置一个虚拟的transport以满足基类的检查
            self.ctx.transport = self._socket
            Log.debug("成功连接到分机")

            return True

        except Exception as e:
            Log.error("连接分机失败: {}", e)
            self._connected = False
            if self._socket:
                self._socket.close()
                self._socket = None
            self.ctx.transport = None
            return False
    
    def close(self) -> None:
        """
        关闭连接
        """
        if self.ctx.trace_connect:
            self.ctx.trace_connect(False)
            
        self._connected = False
        if self._socket:
            try:
                self._socket.close()
            except Exception:
                pass
            finally:
                self._socket = None
        
        super().close()
    
    async def _send_receive_raw(self, data: bytes) -> bytes:
        """
        发送原始数据并接收响应
        
        :param data: 要发送的Modbus RTU数据
        :return: 接收到的响应数据
        """
        if not self._connected or not self._socket:
            raise ConnectionException("未连接到分机")
        
        try:
            # 跟踪发送的数据包
            if self.ctx.trace_packet:
                self.ctx.trace_packet(True, data)

            # 构建rs485命令
            command = self._build_rs485_command(data)
            Log.debug("发送rs485命令: {}", command)

            # 调试输出：显示原始Modbus数据
            self._debug_print(f"原始Modbus数据: {' '.join(f'{b:02X}' for b in data)} ({len(data)} 字节)")

            # 调试输出：显示rs485命令（加密前）
            command_bytes = command.encode('gbk')
            self._debug_print(f"rs485命令(加密前): {command}")
            self._debug_print(f"rs485命令字节(加密前): {' '.join(f'{b:02X}' for b in command_bytes)} ({len(command_bytes)} 字节)")

            # 加密命令
            encrypted_command = self._encrypt_decrypt_data(command_bytes)

            # 调试输出：显示加密后的数据
            self._debug_print(f"加密后数据: {' '.join(f'{b:02X}' for b in encrypted_command)} ({len(encrypted_command)} 字节)")

            # 发送加密的命令到分机
            await asyncio.get_event_loop().run_in_executor(
                None, self._socket.send, encrypted_command
            )
            
            # 接收分机响应
            encrypted_response = await asyncio.get_event_loop().run_in_executor(
                None, self._socket.recv, 1024
            )

            if not encrypted_response:
                raise ConnectionException("分机无响应")

            # 调试输出：显示接收到的加密数据
            self._debug_print(f"接收加密数据: {' '.join(f'{b:02X}' for b in encrypted_response)} ({len(encrypted_response)} 字节)")

            # 解密响应
            decrypted_response = self._encrypt_decrypt_data(encrypted_response)
            response_str = decrypted_response.decode('gbk')

            # 调试输出：显示解密后的数据
            self._debug_print(f"解密后字节: {' '.join(f'{b:02X}' for b in decrypted_response)} ({len(decrypted_response)} 字节)")
            self._debug_print(f"解密后字符串: {response_str}")

            Log.debug("分机响应: {}", response_str)

            # 解析响应为字节数据
            response_data = self._parse_rs485_response(response_str)

            # 调试输出：显示最终解析的Modbus响应数据
            self._debug_print(f"解析后Modbus响应: {' '.join(f'{b:02X}' for b in response_data)} ({len(response_data)} 字节)")
            
            # 跟踪接收的数据包
            if self.ctx.trace_packet:
                self.ctx.trace_packet(False, response_data)
            
            return response_data
            
        except Exception as e:
            Log.error("分机通讯错误: {}", e)
            raise ConnectionException(f"分机通讯失败: {e}")

    async def execute(self, no_response_expected: bool, request: ModbusPDU):
        """
        执行Modbus请求

        :param no_response_expected: 是否期望响应
        :param request: Modbus请求PDU
        :return: 响应PDU
        """
        if not self._connected:
            if not await self.connect():
                raise ConnectionException("无法连接到分机")

        try:
            # 使用framer编码请求
            encoded_request = self.ctx.framer.buildFrame(request)

            # 跟踪PDU
            if self.ctx.trace_pdu:
                self.ctx.trace_pdu(True, request)

            # 发送请求并接收响应
            if no_response_expected:
                # 只发送，不等待响应
                await self._send_receive_raw(encoded_request)
                return None
            else:
                # 发送并等待响应
                response_data = await self._send_receive_raw(encoded_request)

                # 使用framer解码响应
                used_len, response_pdu = self.ctx.framer.processIncomingFrame(response_data)

                # 跟踪响应PDU
                if self.ctx.trace_pdu and response_pdu:
                    self.ctx.trace_pdu(False, response_pdu)

                return response_pdu

        except Exception as e:
            Log.error("执行Modbus请求失败: {}", e)
            raise

    def __str__(self):
        """
        字符串表示

        :return: 客户端描述字符串
        """
        return f"AsyncModbusExtensionClient {self.extension_host}:{self.extension_port}"
