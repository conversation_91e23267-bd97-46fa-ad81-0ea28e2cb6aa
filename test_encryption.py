#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试分机加密/解密算法
"""

def encrypt_decrypt_data_old(data):
    """
    旧的加密/解密方法（对称）
    1. 将字节前后倒转（第一个字节变最后一个）
    2. 对每个字节求补码
    """
    if not data:
        return data

    # 步骤1: 字节前后倒转
    reversed_data = data[::-1]

    # 步骤2: 对每个字节求补码
    encrypted_data = bytes(~b & 0xFF for b in reversed_data)

    return encrypted_data

def encrypt_decrypt_data_v2(data):
    """
    尝试的新加密方法：只求补码，不倒转
    """
    if not data:
        return data

    # 对每个字节求补码
    encrypted_data = bytes(~b & 0xFF for b in data)

    return encrypted_data

def encrypt_decrypt_data_v3(data):
    """
    尝试的新加密方法：只倒转，不求补码
    """
    if not data:
        return data

    # 只倒转字节顺序
    return data[::-1]

def encrypt_decrypt_data_v4(data):
    """
    尝试的新加密方法：先求补码，再倒转
    """
    if not data:
        return data

    # 步骤1: 对每个字节求补码
    complemented_data = bytes(~b & 0xFF for b in data)

    # 步骤2: 字节前后倒转
    encrypted_data = complemented_data[::-1]

    return encrypted_data

# 默认使用旧方法
encrypt_decrypt_data = encrypt_decrypt_data_old

def test_encryption():
    """测试加密解密算法"""
    
    # 测试数据
    test_string = "hello world"
    test_bytes = test_string.encode('utf-8')
    
    print(f"原始字符串: '{test_string}'")
    print(f"原始字节: {' '.join(f'{b:02X}' for b in test_bytes)}")
    
    # 加密
    encrypted = encrypt_decrypt_data(test_bytes)
    print(f"加密后: {' '.join(f'{b:02X}' for b in encrypted)}")
    
    # 解密（应该得到原始数据）
    decrypted = encrypt_decrypt_data(encrypted)
    print(f"解密后: {' '.join(f'{b:02X}' for b in decrypted)}")
    
    try:
        decrypted_string = decrypted.decode('utf-8')
        print(f"解密字符串: '{decrypted_string}'")
        
        if decrypted_string == test_string:
            print("✓ 加密/解密算法正确")
        else:
            print("✗ 加密/解密算法有问题")
    except Exception as e:
        print(f"✗ 解密失败: {e}")

def analyze_modbus_response():
    """分析正常版本的Modbus响应"""

    # 正常版本的最终结果
    modbus_response = bytes.fromhex("01 03 08 01 01 01 14 01 FD 04 98 E6 6F".replace(" ", ""))

    print("=== Modbus响应分析 ===")
    print(f"响应数据: {' '.join(f'{b:02X}' for b in modbus_response)}")
    print(f"长度: {len(modbus_response)} 字节")

    if len(modbus_response) >= 3:
        slave_addr = modbus_response[0]
        func_code = modbus_response[1]
        byte_count = modbus_response[2]

        print(f"从站地址: {slave_addr}")
        print(f"功能码: {func_code}")
        print(f"字节数: {byte_count}")

        if func_code == 3 and len(modbus_response) >= 3 + byte_count + 2:
            # 读取保持寄存器响应
            data_bytes = modbus_response[3:3+byte_count]
            crc_bytes = modbus_response[3+byte_count:3+byte_count+2]

            print(f"数据部分: {' '.join(f'{b:02X}' for b in data_bytes)}")
            print(f"CRC: {' '.join(f'{b:02X}' for b in crc_bytes)}")

            # 解析寄存器值（每个寄存器2字节，大端序）
            registers = []
            for i in range(0, len(data_bytes), 2):
                if i + 1 < len(data_bytes):
                    reg_value = (data_bytes[i] << 8) | data_bytes[i+1]
                    registers.append(reg_value)

            print(f"寄存器值: {registers}")

            # 如果这是传感器数据，尝试解析
            if len(registers) >= 4:
                print("\n=== 传感器数据解析 ===")
                # 假设前4个寄存器是温度、湿度、水分数据
                temp_raw = registers[0]
                hum_raw = registers[1]
                moisture_raw = (registers[2] << 16) | registers[3] if len(registers) > 3 else registers[2]

                # 尝试不同的解析方法
                print(f"原始值: 温度={temp_raw}, 湿度={hum_raw}, 水分={moisture_raw}")
                print(f"除以10: 温度={temp_raw/10:.1f}, 湿度={hum_raw/10:.1f}, 水分={moisture_raw/10:.1f}")
                print(f"除以100: 温度={temp_raw/100:.2f}, 湿度={hum_raw/100:.2f}, 水分={moisture_raw/100:.2f}")

def analyze_real_data():
    """分析实际接收到的数据"""

    print("=== 对比正常工作的版本 ===")

    # 正常工作版本的数据
    working_sent = bytes.fromhex("C8 C9 E0 BA CC E0 CC D0 E0 D0 D0 E0 D0 CF E0 C9 CE E0 CD D0 E0 CF D0 E0 D0 D0 D0 CF E0 8C D3 E0 D0 D0 CA C7 E0 9E D3 E0 CE AD 87 8C 8C D1 8A 9B 9C E0 90 D3 E0 CB C8 CC 8D 8E".replace(" ", ""))
    working_received = bytes.fromhex("F6 BA CA E0 CA BB E0 C8 C7 E0 CC D0 E0 BC BA E0 CF D0 E0 CC CF E0 CF D0 E0 CF D0 E0 CF D0 E0 C8 D0 E0 CD D0 E0 CF D0".replace(" ", ""))
    working_result = bytes.fromhex("01 03 08 01 01 01 14 01 FD 04 98 E6 6F".replace(" ", ""))

    print(f"正常版本发送: {' '.join(f'{b:02X}' for b in working_sent)} ({len(working_sent)} 字节)")
    print(f"正常版本接收: {' '.join(f'{b:02X}' for b in working_received)} ({len(working_received)} 字节)")
    print(f"正常版本结果: {' '.join(f'{b:02X}' for b in working_result)} ({len(working_result)} 字节)")

    # 解密正常版本的发送数据
    working_sent_decrypted = encrypt_decrypt_data(working_sent)
    print(f"\n正常版本发送解密: {' '.join(f'{b:02X}' for b in working_sent_decrypted)}")
    try:
        working_sent_str = working_sent_decrypted.decode('gbk')
        print(f"正常版本发送字符串: '{working_sent_str}'")
    except Exception as e:
        print(f"正常版本发送解码失败: {e}")

    # 解密正常版本的接收数据
    working_received_decrypted = encrypt_decrypt_data(working_received)
    print(f"\n正常版本接收解密: {' '.join(f'{b:02X}' for b in working_received_decrypted)}")
    try:
        working_received_str = working_received_decrypted.decode('gbk')
        print(f"正常版本接收字符串: '{working_received_str}'")
    except Exception as e:
        print(f"正常版本接收解码失败: {e}")

    print("\n=== 我们的版本 ===")

    # 我们发送的原始命令
    original_command = "rs485 -p dev/ttyS2 -b 9600 -t 1000 01 03 27 10 00 04 4F 78"
    original_bytes = original_command.encode('gbk')

    print(f"我们的原始命令: '{original_command}'")
    print(f"我们的命令字节: {' '.join(f'{b:02X}' for b in original_bytes)}")

    # 加密我们的命令
    our_encrypted = encrypt_decrypt_data(original_bytes)
    print(f"我们的加密结果: {' '.join(f'{b:02X}' for b in our_encrypted)}")

    print(f"\n=== 差异分析 ===")
    print(f"命令长度差异: 正常版本{len(working_sent_decrypted)}字节 vs 我们{len(original_bytes)}字节")
    print(f"加密结果差异: 正常版本{len(working_sent)}字节 vs 我们{len(our_encrypted)}字节")

    if working_sent != our_encrypted:
        print("✗ 加密结果不同！")
        # 逐字节比较
        min_len = min(len(working_sent), len(our_encrypted))
        for i in range(min_len):
            if working_sent[i] != our_encrypted[i]:
                print(f"  第{i}字节不同: 正常版本{working_sent[i]:02X} vs 我们{our_encrypted[i]:02X}")
                break
    else:
        print("✓ 加密结果相同")

def find_correct_encryption():
    """尝试找到正确的加密算法"""

    # 已知的正确数据
    original_command = "rs485 -p dev/ttyS2 -b 9600 -t 1000 01 03 27 10 00 04 4F 78"
    original_bytes = original_command.encode('gbk')
    expected_encrypted = bytes.fromhex("C8 C9 E0 BA CC E0 CC D0 E0 D0 D0 E0 D0 CF E0 C9 CE E0 CD D0 E0 CF D0 E0 D0 D0 D0 CF E0 8C D3 E0 D0 D0 CA C7 E0 9E D3 E0 CE AD 87 8C 8C D1 8A 9B 9C E0 90 D3 E0 CB C8 CC 8D 8E".replace(" ", ""))

    print(f"原始命令: '{original_command}'")
    print(f"原始字节: {' '.join(f'{b:02X}' for b in original_bytes)}")
    print(f"期望加密: {' '.join(f'{b:02X}' for b in expected_encrypted)}")

    # 测试不同的加密方法
    methods = [
        ("旧方法(倒转+补码)", encrypt_decrypt_data_old),
        ("方法2(只补码)", encrypt_decrypt_data_v2),
        ("方法3(只倒转)", encrypt_decrypt_data_v3),
        ("方法4(补码+倒转)", encrypt_decrypt_data_v4),
    ]

    for name, method in methods:
        result = method(original_bytes)
        print(f"\n{name}:")
        print(f"结果: {' '.join(f'{b:02X}' for b in result)}")
        if result == expected_encrypted:
            print(f"✓ {name} 匹配！")
            return method
        else:
            print(f"✗ {name} 不匹配")

    print("\n所有已知方法都不匹配，尝试逐字节分析...")

    # 逐字节分析
    print(f"\n逐字节对比 (前10字节):")
    xor_values = []
    for i in range(min(10, len(original_bytes), len(expected_encrypted))):
        orig = original_bytes[i]
        exp = expected_encrypted[i]
        xor_val = orig ^ exp
        xor_values.append(xor_val)
        print(f"位置{i}: 原始{orig:02X}({orig}) -> 期望{exp:02X}({exp}) XOR值{xor_val:02X}")

    # 检查是否有固定的XOR模式
    print(f"\nXOR值序列: {' '.join(f'{x:02X}' for x in xor_values)}")

    # 尝试用固定XOR值加密
    if len(set(xor_values)) == 1:
        # 所有XOR值相同
        xor_key = xor_values[0]
        print(f"发现固定XOR密钥: {xor_key:02X}")

        def encrypt_decrypt_xor_fixed(data):
            return bytes(b ^ xor_key for b in data)

        result = encrypt_decrypt_xor_fixed(original_bytes)
        if result == expected_encrypted:
            print("✓ 固定XOR加密匹配！")
            return encrypt_decrypt_xor_fixed

    # 尝试循环XOR
    for key_len in [1, 2, 4, 8]:
        if len(xor_values) >= key_len:
            xor_key = xor_values[:key_len]
            print(f"尝试{key_len}字节循环XOR密钥: {' '.join(f'{x:02X}' for x in xor_key)}")

            def encrypt_decrypt_xor_cycle(data, key=xor_key):
                return bytes(data[i] ^ key[i % len(key)] for i in range(len(data)))

            result = encrypt_decrypt_xor_cycle(original_bytes)
            if result == expected_encrypted:
                print(f"✓ {key_len}字节循环XOR加密匹配！")
                return lambda data: encrypt_decrypt_xor_cycle(data, xor_key)

    return None

if __name__ == "__main__":
    print("=== 分析Modbus响应 ===")
    analyze_modbus_response()

    print("\n=== 寻找正确的加密算法 ===")
    correct_method = find_correct_encryption()

    if correct_method:
        print(f"\n找到正确的加密方法！")
    else:
        print(f"\n未找到匹配的加密方法，需要进一步分析")

    print("\n=== 测试加密/解密算法 ===")
    test_encryption()

    print("\n=== 分析实际数据 ===")
    analyze_real_data()
