#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试分机加密/解密算法
"""

def encrypt_decrypt_data(data):
    """
    分机加密/解密方法（对称）
    1. 将字节前后倒转（第一个字节变最后一个）
    2. 对每个字节求补码
    """
    if not data:
        return data
        
    # 步骤1: 字节前后倒转
    reversed_data = data[::-1]
    
    # 步骤2: 对每个字节求补码
    encrypted_data = bytes(~b & 0xFF for b in reversed_data)
    
    return encrypted_data

def test_encryption():
    """测试加密解密算法"""
    
    # 测试数据
    test_string = "hello world"
    test_bytes = test_string.encode('utf-8')
    
    print(f"原始字符串: '{test_string}'")
    print(f"原始字节: {' '.join(f'{b:02X}' for b in test_bytes)}")
    
    # 加密
    encrypted = encrypt_decrypt_data(test_bytes)
    print(f"加密后: {' '.join(f'{b:02X}' for b in encrypted)}")
    
    # 解密（应该得到原始数据）
    decrypted = encrypt_decrypt_data(encrypted)
    print(f"解密后: {' '.join(f'{b:02X}' for b in decrypted)}")
    
    try:
        decrypted_string = decrypted.decode('utf-8')
        print(f"解密字符串: '{decrypted_string}'")
        
        if decrypted_string == test_string:
            print("✓ 加密/解密算法正确")
        else:
            print("✗ 加密/解密算法有问题")
    except Exception as e:
        print(f"✗ 解密失败: {e}")

def analyze_real_data():
    """分析实际接收到的数据"""
    
    # 这是我们从分机接收到的实际加密数据
    encrypted_data = bytes.fromhex("F6 9C 92 8B 91 9A E0 8C 91 92 E0 C6 C7 C8 DF B9 CB DF CB CF DF CF CF DF CF CE DF C8 CD DF CC CF DF CE CF DF CF CF CF CC DF 8B D2 DF CF CF C9 C6 DF 9D D2 DF CD AC 86 8B 8B D0 89 9A 9B DF 8F D2 DF CA C7 CB 8C 8D E0 C6 98 8D".replace(" ", ""))
    
    print(f"\n实际接收的加密数据:")
    print(f"长度: {len(encrypted_data)} 字节")
    print(f"数据: {' '.join(f'{b:02X}' for b in encrypted_data)}")
    
    # 解密
    decrypted = encrypt_decrypt_data(encrypted_data)
    print(f"\n解密后数据:")
    print(f"长度: {len(decrypted)} 字节")
    print(f"字节: {' '.join(f'{b:02X}' for b in decrypted)}")
    
    try:
        decrypted_string = decrypted.decode('gbk')
        print(f"解密字符串: '{decrypted_string}'")
    except Exception as e:
        print(f"GBK解码失败: {e}")
        try:
            decrypted_string = decrypted.decode('utf-8')
            print(f"UTF-8解密字符串: '{decrypted_string}'")
        except Exception as e2:
            print(f"UTF-8解码也失败: {e2}")
    
    # 我们发送的原始命令
    original_command = "rs485 -p dev/ttyS2 -b 9600 -t 3000 01 03 27 10 00 04 4F 78"
    original_bytes = original_command.encode('gbk')
    
    print(f"\n我们发送的原始命令:")
    print(f"字符串: '{original_command}'")
    print(f"字节: {' '.join(f'{b:02X}' for b in original_bytes)}")
    
    # 加密我们的命令
    encrypted_command = encrypt_decrypt_data(original_bytes)
    print(f"\n我们命令的加密结果:")
    print(f"字节: {' '.join(f'{b:02X}' for b in encrypted_command)}")
    
    # 检查是否匹配
    if encrypted_command in encrypted_data:
        print("✓ 接收到的数据包含我们发送的加密命令")
    else:
        print("✗ 接收到的数据不包含我们发送的加密命令")

if __name__ == "__main__":
    print("=== 测试加密/解密算法 ===")
    test_encryption()
    
    print("\n=== 分析实际数据 ===")
    analyze_real_data()
